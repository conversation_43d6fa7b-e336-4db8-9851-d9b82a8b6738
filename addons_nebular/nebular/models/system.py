# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class NebularSystem(models.Model):
    """Main System model for the Nebular Dashboard System"""
    
    # 1. Private attributes
    _name = 'nebular.system'
    _description = 'System Monitoring'
    _inherit = ['mail.thread']


    
    # 2. Default methods
    @api.model
    def _default_icon_color(self):
        """Default icon color"""
        return '#007bff'
    
    # 3. Field declarations
    # Basic Information
    name = fields.Char(
        required=True,
        index=True,
        tracking=True,
        help='name of the system'
    )
    
    code = fields.Char(
        string='System Code',
        required=True,
        index=True,
        tracking=True,
        help='Unique code for the system'
    )
    
    system_type = fields.Selection([
        ('hvac', 'HVAC'),
        ('cctv', 'CCTV Surveillance'),
        ('lighting', 'Lighting'),
        ('security', 'Security'),
        ('fire_safety', 'Fire Safety'),
        ('fire_alarm', 'Fire Alarm'),
        ('access_control', 'Access Control'),
        ('pa', 'Public Address'),
        ('presence', 'Presence Detection'),
        ('energy', 'Energy Management'),
        ('other', 'Other')
    ], string='System Type', required=True, tracking=True, help='Type of system')
    
    manufacturer = fields.Char(
        string='Manufacturer',
        tracking=True,
        help='System manufacturer'
    )
    
    model = fields.Char(
        string='Model',
        tracking=True,
        help='System model'
    )
    
    building_id = fields.Many2one(
        comodel_name='nebular.building',
        string='Building',
        tracking=True,
        help='Building where this system is located'
    )
    
    icon_name = fields.Char(
        string='Icon Name',
        required=True,
        tracking=True,
        help='Font Awesome icon name (e.g., fa-fire, fa-camera, fa-shield-alt)'
    )
    
    icon_color = fields.Char(
        string='Icon Color',
        required=True,
        default=_default_icon_color,
        tracking=True,
        help='Hex color code for the icon (e.g., #ff0000)'
    )
    
    description = fields.Text(
        string='Description',
        help='Detailed description of the system'
    )
    
    # Status fields
    is_active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True,
        help='Whether this system is active'
    )
    
    status = fields.Selection([
        ('online', 'Online'),
        ('offline', 'Offline'),
        ('maintenance', 'Maintenance'),
        ('error', 'Error')
    ], string='Status', default='offline', tracking=True, help='Current system status')
    
    health_status = fields.Selection([
        ('healthy', 'Healthy'),
        ('warning', 'Warning'),
        ('critical', 'Critical')
    ], string='Health Status', default='healthy', tracking=True, help='System health status')
    
    last_communication = fields.Datetime(
        string='Last Communication',
        tracking=True,
        help='Last time the system communicated'
    )
    
    # Relationships
    event_ids = fields.One2many(
        comodel_name='nebular.event',
        inverse_name='system_id',
        string='Events',
        help='Events related to this system'
    )

    device_ids = fields.One2many(
        comodel_name='nebular.device',
        inverse_name='system_id',
        string='Devices',
        help='Devices related to this system'
    )

    door_ids = fields.One2many(
        comodel_name='nebular.door',
        inverse_name='system_id',
        string='Doors',
        help='Doors related to this system'
    )
    
    # 4. Computed fields
    event_count = fields.Integer(
        string='Event Count',
        compute='_compute_event_count',
        store=True,
        help='Number of events'
    )
    
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True,
        help='Display name for the system'
    )
    
    @api.depends('name', 'code')
    def _compute_display_name(self):
        """Compute display name"""
        for system in self:
            if system.code:
                system.display_name = f"{system.name or ''} ({system.code})"
            else:
                system.display_name = system.name or "New System"
    
    # 5. Compute methods
    @api.depends('event_ids')
    def _compute_event_count(self):
        """Count events"""
        for system in self:
            system.event_count = len(system.event_ids)

    
    # 6. Constraints
    @api.constrains('icon_color')
    def _check_icon_color(self):
        """Validate icon color format"""
        for system in self:
            if system.icon_color and not system.icon_color.startswith('#'):
                raise ValidationError("Icon color must be a valid hex color code starting with #")
            if system.icon_color and len(system.icon_color) not in [4, 7]:  # #RGB or #RRGGBB
                raise ValidationError("Icon color must be in format #RGB or #RRGGBB")
    

    
    # 7. Action methods
    def action_activate(self):
        """Activate the system"""
        self.ensure_one()
        self.is_active = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"System '{self.name}' has been activated.",
                'type': 'success',
            }
        }
    
    def action_deactivate(self):
        """Deactivate the system"""
        self.ensure_one()
        self.is_active = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"System '{self.name}' has been deactivated.",
                'type': 'warning',
            }
        }
    
    def action_start_maintenance(self):
        """Start maintenance mode for the system"""
        self.ensure_one()
        # This would typically set a maintenance flag or create a maintenance record
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Maintenance started for system '{self.name}'.",
                'type': 'info',
            }
        }
    
    def action_complete_maintenance(self):
        """Complete maintenance for the system"""
        self.ensure_one()
        # This would typically clear maintenance flags or update maintenance records
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f"Maintenance completed for system '{self.name}'.",
                'type': 'success',
            }
        }
    
    def action_view_events(self):
        """View system events"""
        self.ensure_one()
        return {
            'name': f'Events - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'nebular.event',
            'view_mode': 'list,form',
            'domain': [('system_id', '=', self.id)],
            'context': {'default_system_id': self.id},
        }

    # 8. Business methods