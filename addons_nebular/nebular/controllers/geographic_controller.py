from odoo import http
from odoo.http import request, _logger
import json
from datetime import datetime, timedelta
from .base_controller import BaseController
from ..api.utils import (
    validate_system_code, validate_event_type_for_system,
    format_iso_datetime, parse_iso_datetime, safe_int_conversion
)

class GeographicController(BaseController):
    """
    Controller for managing buildings via RESTful API.
     Controller for managing floors via RESTful API.
    """

    @property
    def building_model(self):
        """Get building model with sudo access"""
        return self.request_context.env['nebular.building'].sudo()

    @property
    def floor_model(self):
        """Get floor model with sudo access"""
        return self.request_context.env['nebular.floor'].sudo()

    @http.route('/api/v1/buildings', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_buildings(self, name=None, code=None, is_active=None,
                      has_alerts=None, zone_count=None, limit=100, offset=0, search=None, **kwargs):
        """
        Get list of buildings with optional filtering and pagination.

        Query Parameters:
        - name: Filter by building name
        - code: Filter by building code
        - is_active: Filter by active status (true/false)
        - has_alerts: Filter by alert status (true/false)
        - zone_count: Filter by exact zone count
        - limit: Maximum number of results (default: 100)
        - offset: Pagination offset (default: 0)
        - search: Search in name and code fields
        """
        try:
            self.log_api_access('/api/v1/buildings', 'GET', {
                'name': name, 'code': code, 'is_active': is_active,
                'has_alerts': has_alerts, 'zone_count': zone_count,
                'limit': limit, 'offset': offset
            })

            # Build domain for filtering
            domain = []

            # Filter by name
            if name:
                domain.append(('name', 'ilike', name))

            # Filter by code
            if code:
                domain.append(('code', 'ilike', code))


            # Filter by active status
            if is_active is not None:
                if is_active.lower() in ['true', '1', 'yes']:
                    domain.append(('is_active', '=', True))
                elif is_active.lower() in ['false', '0', 'no']:
                    domain.append(('is_active', '=', False))

            # Filter by alert status
            if has_alerts is not None:
                if has_alerts.lower() in ['true', '1', 'yes']:
                    domain.append(('has_alerts', '=', True))
                elif has_alerts.lower() in ['false', '0', 'no']:
                    domain.append(('has_alerts', '=', False))

            # Filter by exact zone count
            if zone_count is not None:
                domain.append(('zone_count', '=', safe_int_conversion(zone_count)))

            # Search in multiple fields - الطريقة الصحيحة
            if search:
                domain.extend([
                    '|', '|', '|',
                    ('name', 'ilike', search),
                    ('code', 'ilike', search),
                    ('description', 'ilike', search),
                    ('address', 'ilike', search)
                ])

            # Convert limit and offset to integers
            limit = safe_int_conversion(limit, 100)
            offset = safe_int_conversion(offset, 0)

            # Validate limit
            if limit > 1000:
                limit = 1000  # Cap at 1000 for performance

            # Get buildings with pagination
            buildings = self.building_model.search(
                domain,
                limit=limit,
                offset=offset,
                order='name'
            )

            # Get total count for pagination info
            total_count = self.building_model.search_count(domain)

            # Convert to list of dictionaries
            buildings_list = [self._building_to_dict(building) for building in buildings]

            result = {
                "response_code": "000",
                "response_message": "Success",
                "response_message_ar": "نجح",
                "data": {
                    "buildings": buildings_list,
                    "total_count": total_count,
                    "returned_count": len(buildings_list),
                    "pagination": {
                        "limit": limit,
                        "offset": offset,
                        "has_more": (offset + len(buildings_list)) < total_count
                    }
                }
            }

            return request.make_json_response(result, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

    def _building_to_dict(self, building):
        """Convert building record to dictionary"""
        return {
            'id': building.id,
            'name': building.name or '',
            'code': building.code or '',
            'description': building.description or '',
            'address': building.address or '',
            'is_active': building.is_active,

            # Counts - هذه الحقول موجودة في الـ computed fields
            'zone_count': building.zone_count,
            'floor_count': building.floor_count,
            'room_count': building.room_count,
            'door_count': building.door_count,
            'device_count': building.device_count,
            'event_count': building.event_count,
            'has_alerts': building.has_alerts,

            # Timestamps
            'create_date': building.create_date.isoformat() if building.create_date else '',
            'write_date': building.write_date.isoformat() if building.write_date else '',

            # Quick access URLs
            'zones_url': f"/api/v1/buildings/{building.id}/zones",
            'floors_url': f"/api/v1/buildings/{building.id}/floors",
            'devices_url': f"/api/v1/buildings/{building.id}/devices"
        }
    # 2. GET - الحصول على مبنى محدد
    @http.route('/api/v1/buildings/<int:building_id>', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_building(self, building_id, **kwargs):
        """
        Get single building by ID with optional related data.

        Path Parameters:
        - building_id: Building ID

        Query Parameters:
        - include_zones: Include zones data (true/false)
        - include_floors: Include floors data (true/false)
        - include_devices: Include devices count by type (true/false)
        - include_events: Include recent events (true/false)
    """
        try:
            self.log_api_access(f'/api/v1/buildings/{building_id}', 'GET', kwargs)

            # Get building by ID
            building = self.building_model.browse(building_id)

            if not building.exists():
                return self.handle_api_error(
                    f"Building with ID {building_id} not found",
                    "404", 404, "NotFound"
                )

            # Check if building is active (optional)
            if not building.is_active and not kwargs.get('include_inactive'):
                return self.handle_api_error(
                    f"Building with ID {building_id} is not active",
                    "403", 403, "InactiveBuilding"
                )

            # Determine what related data to include
            include_zones = kwargs.get('include_zones', '').lower() in ['true', '1', 'yes']
            include_floors = kwargs.get('include_floors', '').lower() in ['true', '1', 'yes']
            include_devices = kwargs.get('include_devices', '').lower() in ['true', '1', 'yes']
            include_events = kwargs.get('include_events', '').lower() in ['true', '1', 'yes']

            # Convert building to dictionary with optional related data
            building_data = self._building_to_detail_dict(
                building,
                include_zones,
                include_floors,
                include_devices,
                include_events
            )

            result = {
                "response_code": "000",
                "response_message": "Success",
                "response_message_ar": "نجح",
                "data": building_data
            }

            return request.make_json_response(result, status=200)

        except Exception as ex:

            return self.handle_api_error(ex)

    def _building_to_detail_dict(self, building, include_zones=False, include_floors=False,
                                 include_devices=False, include_events=False):
        """Convert building record to detailed dictionary with optional related data"""

        building_data = {
            'id': building.id,
            'name': building.name or '',
            'code': building.code or '',
            'description': building.description or '',
            'address': building.address or '',
            'is_active': building.is_active,

            # Counts
            'zone_count': building.zone_count,
            'floor_count': building.floor_count,
            'room_count': building.room_count,
            'door_count': building.door_count,
            'device_count': building.device_count,
            'event_count': building.event_count,
            'has_alerts': building.has_alerts,

            # Timestamps
            'create_date': building.create_date.isoformat() if building.create_date else '',
            'write_date': building.write_date.isoformat() if building.write_date else '',

            # Quick access URLs
            'zones_url': f"/api/v1/buildings/{building.id}/zones",
            'floors_url': f"/api/v1/buildings/{building.id}/floors",
            'devices_url': f"/api/v1/buildings/{building.id}/devices",
            'events_url': f"/api/v1/buildings/{building.id}/events"
        }

        # Include zones data if requested
        if include_zones:
            building_data['zones'] = self._get_building_zones(building)

        # Include floors data if requested
        if include_floors:
            building_data['floors'] = self._get_building_floors(building)

        # Include devices summary if requested
        if include_devices:
            building_data['devices_summary'] = self._get_building_devices_summary(building)

        # Include recent events if requested
        if include_events:
            building_data['recent_events'] = self._get_building_recent_events(building)

        return building_data

    def _get_building_zones(self, building):
        """Get detailed zones data for a building"""
        zones = []
        for zone in building.zone_ids:
            zones.append({
                'id': zone.id,
                'name': zone.name,
                'code': zone.code,
                'description': zone.description or '',
                'is_active': zone.is_active,
                'device_count': zone.device_count,
                'floor_count': zone.floor_count,
                'has_alerts': any(device.is_alert for device in zone.device_ids)
            })
        return zones

    def _get_building_floors(self, building):
        """Get detailed floors data for a building"""
        floors = []
        for floor in building.floor_ids:
            floors.append({
                'id': floor.id,
                'name': floor.name,
                'code': floor.code,
                'level': floor.floor_number,
                'description': floor.description or '',
                'room_count': floor.room_count,
                'device_count': floor.device_count,
                'zone_name': floor.zone_id.name if floor.zone_id else '',
                'is_active': floor.is_active,
                'has_alerts': floor.has_alerts
            })
        return floors

    def _get_building_devices_summary(self, building):
        """Get devices count grouped by type and status"""
        devices = building.device_ids
        summary = {
            'total': len(devices),
            'by_type': {},
            'by_status': {
                'active': len(devices.filtered(lambda d: d.is_active)),
                'inactive': len(devices.filtered(lambda d: not d.is_active)),
                'alert': len(devices.filtered(lambda d: d.is_alert))
            }
        }

        # Group by device type
        for device in devices:
            device_type = device.device_type or 'Unknown'
            if device_type not in summary['by_type']:
                summary['by_type'][device_type] = 0
            summary['by_type'][device_type] += 1

        return summary

    def _get_building_recent_events(self, building, limit=10):
        """Get recent events for a building"""
        event_model = self.request_context.env['nebular.event'].sudo()
        domain = [
            ('building_id', '=', building.id),
            ('create_date', '>=', datetime.now() - timedelta(days=30))
        ]

        events = event_model.search(domain, limit=limit, order='create_date desc')

        events_list = []
        for event in events:
            events_list.append({
                'id': event.id,
                'event_type': event.event_type or '',
                'message': event.message or '',
                'system_code': event.system_id.code if event.system_id else '',
                'create_date': event.create_date.isoformat() if event.create_date else '',
                'severity': event.severity or 'info'
            })

        return events_list

    @http.route('/api/v1/floors', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_floors(self, name=None, code=None, is_active=None, has_alerts=None,
                   building_id=None, zone_id=None, floor_number=None,
                   has_floor_plan=None, room_count=None,
                   limit=100, offset=0, search=None, **kwargs):
        """
        Get list of floors with optional filtering and pagination.

        Query Parameters:
        - name: Filter by floor name
        - code: Filter by floor code
        - is_active: Filter by active status (true/false)
        - has_alerts: Filter by alert status (true/false)
        - building_id: Filter by building ID
        - zone_id: Filter by zone ID
        - floor_number: Filter by floor number
        - has_floor_plan: Filter by floor plan availability (true/false)
        - room_count_min: Minimum room count
        - room_count_max: Maximum room count
        - limit: Maximum number of results (default: 100)
        - offset: Pagination offset (default: 0)
        - search: Search in name and code fields
        """
        try:
            self.log_api_access('/api/v1/floors', 'GET', {
                'name': name, 'code': code, 'is_active': is_active,
                'has_alerts': has_alerts, 'building_id': building_id,
                'zone_id': zone_id, 'floor_number': floor_number,
                'limit': limit, 'offset': offset
            })

            # Build domain for filtering
            domain = []

            # Filter by name
            if name:
                domain.append(('name', 'ilike', name))

            # Filter by code
            if code:
                domain.append(('code', 'ilike', code))

            # Filter by active status
            if is_active is not None:
                if is_active.lower() in ['true', '1', 'yes']:
                    domain.append(('is_active', '=', True))
                elif is_active.lower() in ['false', '0', 'no']:
                    domain.append(('is_active', '=', False))

            # Filter by alert status
            if has_alerts is not None:
                if has_alerts.lower() in ['true', '1', 'yes']:
                    domain.append(('has_alerts', '=', True))
                elif has_alerts.lower() in ['false', '0', 'no']:
                    domain.append(('has_alerts', '=', False))

            # Filter by building
            if building_id:
                domain.append(('building_id', '=', safe_int_conversion(building_id)))

            # Filter by zone
            if zone_id:
                domain.append(('zone_id', '=', safe_int_conversion(zone_id)))

            # Filter by floor number
            if floor_number is not None:
                domain.append(('floor_number', '=', safe_int_conversion(floor_number)))

            # Filter by floor plan availability
            if has_floor_plan is not None:
                if has_floor_plan.lower() in ['true', '1', 'yes']:
                    domain.append(('has_floor_plan', '=', True))
                elif has_floor_plan.lower() in ['false', '0', 'no']:
                    domain.append(('has_floor_plan', '=', False))

            # Filter by room count range
            if room_count is not None:
                domain.append(('room_count', '=', safe_int_conversion(room_count)))


            # Search in multiple fields
            if search:
                domain.extend([
                    '|', '|', '|',
                    ('name', 'ilike', search),
                    ('code', 'ilike', search),
                    ('description', 'ilike', search),
                    ('building_id.name', 'ilike', search)
                ])

            # Convert limit and offset to integers
            limit = safe_int_conversion(limit, 100)
            offset = safe_int_conversion(offset, 0)

            # Validate limit
            if limit > 1000:
                limit = 1000

            # Get floors with pagination
            floors = self.floor_model.search(
                domain,
                limit=limit,
                offset=offset,
                order="name"
            )

            # Get total count for pagination info
            total_count = self.floor_model.search_count(domain)

            # Convert to list of dictionaries
            floors_list = [self._floor_to_dict(floor) for floor in floors]

            result = {
                "response_code": "000",
                "response_message": "Success",
                "response_message_ar": "نجح",
                "data": {
                    "floors": floors_list,
                    "total_count": total_count,
                    "returned_count": len(floors_list),
                    "pagination": {
                        "limit": limit,
                        "offset": offset,
                        "has_more": (offset + len(floors_list)) < total_count
                    }
                }
            }

            return request.make_json_response(result, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

    def _floor_to_dict(self, floor):
        """Convert floor record to dictionary"""
        return {
            'id': floor.id,
            'name': floor.name or '',
            'code': floor.code or '',
            'floor_number': floor.floor_number,
            'description': floor.description or '',
            'display_name': floor.display_name or '',

            # Status fields
            'is_active': floor.is_active,
            'has_alerts': floor.has_alerts,
            'has_floor_plan': floor.has_floor_plan,

            # Counts
            'room_count': floor.room_count,
            'door_count': floor.door_count,
            'device_count': floor.device_count,
            'marker_count': floor.marker_count,
            'event_count': floor.event_count,

            # Relationships - basic info
            'building': {
                'id': floor.building_id.id,
                'name': floor.building_id.name or '',
                'code': floor.building_id.code or ''
            } if floor.building_id else None,

            'zone': {
                'id': floor.zone_id.id,
                'name': floor.zone_id.name or '',
                'code': floor.zone_id.code or ''
            } if floor.zone_id else None,

            # Timestamps
            'create_date': floor.create_date.isoformat() if floor.create_date else '',
            'write_date': floor.write_date.isoformat() if floor.write_date else '',

            # Quick access URLs
            'building_url': f"/api/v1/buildings/{floor.building_id.id}" if floor.building_id else '',
            'zone_url': f"/api/v1/zones/{floor.zone_id.id}" if floor.zone_id else '',
            'rooms_url': f"/api/v1/floors/{floor.id}/rooms",
            'devices_url': f"/api/v1/floors/{floor.id}/devices",
            'markers_url': f"/api/v1/floors/{floor.id}/markers"
        }

    @http.route('/api/v1/floors/<int:floor_id>', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_floor(self, floor_id, **kwargs):
        """
        Get single floor by ID with optional related data.

        Path Parameters:
        - floor_id: Floor ID

        Query Parameters:
        - include_rooms: Include rooms data (true/false)
        - include_devices: Include devices data (true/false)
        - include_markers: Include markers data (true/false)
        - include_events: Include recent events (true/false)
        """
        try:
            self.log_api_access(f'/api/v1/floors/{floor_id}', 'GET', kwargs)

            # Get floor by ID
            floor = self.floor_model.browse(floor_id)

            if not floor.exists():
                return self.handle_api_error(
                    f"Floor with ID {floor_id} not found",
                    "404", 404, "NotFound"
                )

            # Check if floor is active (optional)
            if not floor.is_active and not kwargs.get('include_inactive'):
                return self.handle_api_error(
                    f"Floor with ID {floor_id} is not active",
                    "403", 403, "InactiveFloor"
                )

            # Determine what related data to include
            include_rooms = kwargs.get('include_rooms', '').lower() in ['true', '1', 'yes']
            include_devices = kwargs.get('include_devices', '').lower() in ['true', '1', 'yes']
            include_markers = kwargs.get('include_markers', '').lower() in ['true', '1', 'yes']
            include_events = kwargs.get('include_events', '').lower() in ['true', '1', 'yes']

            # Convert floor to dictionary with optional related data
            floor_data = self._floor_to_detail_dict(
                floor,
                include_rooms,
                include_devices,
                include_markers,
                include_events
            )

            result = {
                "response_code": "000",
                "response_message": "Success",
                "response_message_ar": "نجح",
                "data": floor_data
            }

            return request.make_json_response(result, status=200)

        except Exception as ex:
            return self.handle_api_error(ex)

    def _floor_to_detail_dict(self, floor, include_rooms=False, include_devices=False,
                              include_markers=False, include_events=False):
        """Convert floor record to detailed dictionary with optional related data"""

        floor_data = {
            'id': floor.id,
            'name': floor.name or '',
            'code': floor.code or '',
            'floor_number': floor.floor_number,
            'description': floor.description or '',
            'display_name': floor.display_name or '',

            # Status fields
            'is_active': floor.is_active,
            'has_alerts': floor.has_alerts,
            'has_floor_plan': floor.has_floor_plan,

            # Counts
            'room_count': floor.room_count,
            'door_count': floor.door_count,
            'device_count': floor.device_count,
            'marker_count': floor.marker_count,
            'event_count': floor.event_count,

            # Relationships - detailed info
            'building': {
                'id': floor.building_id.id,
                'name': floor.building_id.name or '',
                'code': floor.building_id.code or '',
                'address': floor.building_id.address or ''
            } if floor.building_id else None,

            'zone': {
                'id': floor.zone_id.id,
                'name': floor.zone_id.name or '',
                'code': floor.zone_id.code or '',
                'description': floor.zone_id.description or ''
            } if floor.zone_id else None,

            # Floor plan info
            'floor_plan': {
                'has_image': bool(floor.floor_plan_image),
                'filename': floor.floor_plan_filename or ''
            },

            # Timestamps
            'create_date': floor.create_date.isoformat() if floor.create_date else '',
            'write_date': floor.write_date.isoformat() if floor.write_date else ''
        }

        # Include rooms data if requested
        if include_rooms:
            floor_data['rooms'] = self._get_floor_rooms(floor)

        # Include devices data if requested
        if include_devices:
            floor_data['devices'] = self._get_floor_devices(floor)

        # Include markers data if requested
        if include_markers:
            floor_data['markers'] = self._get_floor_markers(floor)

        # Include recent events if requested
        if include_events:
            floor_data['recent_events'] = self._get_floor_recent_events(floor)

        return floor_data

    def _get_floor_rooms(self, floor):
        """Get detailed rooms data for a floor"""
        rooms = []
        for room in floor.room_ids:
            rooms.append({
                'id': room.id,
                'name': room.name,
                'code': room.code,
                'description': room.description or '',
                'room_type': room.room_type or '',
                'is_active': room.is_active,
                'device_count': room.device_count,
                'has_alerts': any(device.is_alert for device in room.device_ids)
            })
        return rooms

    def _get_floor_devices(self, floor):
        """Get detailed devices data for a floor"""
        devices = []
        for device in floor.device_ids:
            devices.append({
                'id': device.id,
                'name': device.name,
                'code': device.code,
                'device_type': device.device_type or '',
                'model': device.model or '',
                'ip_address': device.ip_address or '',
                'is_active': device.is_active,
                'is_alert': device.is_alert,
                'last_maintenance': device.last_maintenance.isoformat() if device.last_maintenance else ''
            })
        return devices

    def _get_floor_markers(self, floor):
        """Get detailed markers data for a floor"""
        markers = []
        for marker in floor.marker_ids:
            markers.append({
                'id': marker.id,
                'name': marker.name,
                'marker_type': marker.marker_type or '',
                'x_position': marker.x_position,
                'y_position': marker.y_position,
                'is_active': marker.is_active,
                'has_alerts': marker.has_alerts,
                'related_device': {
                    'id': marker.device_id.id,
                    'name': marker.device_id.name
                } if marker.device_id else None
            })
        return markers

    def _get_floor_recent_events(self, floor, limit=10):
        """Get recent events for a floor"""
        event_model = request.env['nebular.event'].sudo()
        domain = [
            ('floor_id', '=', floor.id),
            ('create_date', '>=', datetime.now() - timedelta(days=30))
        ]

        events = event_model.search(domain, limit=limit, order='create_date desc')

        events_list = []
        for event in events:
            events_list.append({
                'id': event.id,
                'event_type': event.event_type or '',
                'message': event.message or '',
                'system_code': event.system_id.code if event.system_id else '',
                'create_date': event.create_date.isoformat() if event.create_date else '',
                'severity': event.severity or 'info'
            })

        return events_list
