from odoo import http
from odoo.http import request
from datetime import datetime
from .base_controller import BaseController
from ..api.event_dto import EventDTO, EventListDTO
from ..api.utils import (
    validate_system_code, validate_event_type_for_system, 
    format_iso_datetime, parse_iso_datetime, safe_int_conversion
)


class EventController(BaseController):
    """
    RESTful API Controller for Event management.
    Supports all system types: fire, access, cctv, gate, pa, presence
    """
    
    @property
    def event_model(self):
        """Get event model with sudo access"""
        return self.request_context.env['nebular.event'].sudo()
    
    @http.route('/api/v1/events', type='http', auth='public', 
                methods=['GET'], csrf=False, cors="*")
    def get_events(self, system_code=None, event_type=None, building_id=None, 
                   floor_id=None, date_from=None, date_to=None, 
                   limit=100, offset=0, search=None, **kwargs):
        """
        Get list of events with optional filtering and pagination.
        
        Query Parameters:
        - system_code: Filter by system (fire|access|cctv|gate|pa|presence)
        - event_type: Filter by event type
        - building_id: Filter by building ID
        - floor_id: Filter by floor ID
        - date_from: Filter from date (ISO-8601 format)
        - date_to: Filter to date (ISO-8601 format)
        - limit: Maximum number of results (default: 100)
        - offset: Pagination offset (default: 0)
        - search: Search in message field
        """
        try:
            self.log_api_access('/api/v1/events', 'GET', {
                'system_code': system_code, 'event_type': event_type,
                'building_id': building_id, 'floor_id': floor_id,
                'limit': limit, 'offset': offset
            })
            
            # Build domain for filtering
            domain = []
            
            if system_code:
                if not validate_system_code(system_code):
                    return self.handle_api_error(
                        f"Invalid system_code: {system_code}", "101", 400, "ValidationError"
                    )
                domain.append(('system_id.code', '=', system_code))
            
            if event_type:
                domain.append(('event_type', '=', event_type))
            
            if building_id:
                domain.append(('building_id', '=', safe_int_conversion(building_id)))
            
            if floor_id:
                domain.append(('floor_id', '=', safe_int_conversion(floor_id)))
            
            if date_from:
                try:
                    dt_from = parse_iso_datetime(date_from)
                    domain.append(('event_time', '>=', dt_from))
                except ValueError:
                    return self.handle_api_error(
                        "Invalid date_from format. Use ISO-8601", "101", 400, "ValidationError"
                    )
            
            if date_to:
                try:
                    dt_to = parse_iso_datetime(date_to)
                    domain.append(('event_time', '<=', dt_to))
                except ValueError:
                    return self.handle_api_error(
                        "Invalid date_to format. Use ISO-8601", "101", 400, "ValidationError"
                    )
            
            if search:
                domain.append(('message', 'ilike', search))
            
            # Convert limit and offset to integers
            limit = safe_int_conversion(limit, 100)
            offset = safe_int_conversion(offset, 0)
            
            # Validate limit
            if limit > 1000:
                limit = 1000  # Cap at 1000 for performance
            
            # Get events with pagination
            events = self.event_model.search(
                domain, 
                limit=limit, 
                offset=offset, 
                order='event_time desc'
            )
            
            # Get total count for pagination info
            total_count = self.event_model.search_count(domain)
            
            # Convert to DTOs
            event_dtos = [self._event_to_dto(event) for event in events]
            
            result = EventListDTO(
                response_code="000",
                response_message="Success",
                response_message_ar="نجح",
                events=event_dtos,
                total_count=total_count
            )
            
            return request.make_json_response(result.to_dict(), status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/v1/event/<int:event_id>', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_event(self, event_id, **kwargs):
        """
        Get single event by ID.
        
        Path Parameters:
        - event_id: Event ID
        """
        try:
            self.log_api_access(f'/api/v1/event/{event_id}', 'GET')
            
            event = self.event_model.browse(event_id)
            if not event.exists():
                return self.handle_api_error(
                    f"Event with ID {event_id} not found", "404", 404, "NotFound"
                )
            
            result = self._event_to_dto(event)
            result.response_code = "000"
            result.response_message = "Success"
            result.response_message_ar = "نجح"
            
            return request.make_json_response(result.to_dict(), status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/v1/event', type='http', auth='public',
                methods=['POST'], csrf=False, cors="*")
    def create_event(self, **kwargs):
        """
        Create new event.
        
        Request Body: JSON with event data following the common event envelope structure
        """
        try:
            data = request.get_json_data()
            if not data:
                return self.handle_api_error(
                    "Request body is required", "101", 400, "ValidationError"
                )
            
            self.log_api_access('/api/v1/event', 'POST', data)
            
            # Validate required fields
            required_fields = ['buildingId', 'systemCode', 'eventType', 'datetime', 'message']
            self.validate_required_params(data, required_fields)
            
            # Validate system_code
            system_code = data.get('systemCode')
            if not validate_system_code(system_code):
                return self.handle_api_error(
                    f"Invalid systemCode: {system_code}", "101", 400, "ValidationError"
                )
            
            # Validate event_type for system
            event_type = data.get('eventType')
            if not validate_event_type_for_system(system_code, event_type):
                return self.handle_api_error(
                    f"Invalid eventType '{event_type}' for system '{system_code}'", 
                    "101", 400, "ValidationError"
                )
            
            # Parse datetime
            try:
                event_datetime = parse_iso_datetime(data.get('datetime'))
                if event_datetime == datetime.min:
                    raise ValueError("Invalid datetime format")
            except ValueError:
                return self.handle_api_error(
                    "Invalid datetime format. Use ISO-8601", "101", 400, "ValidationError"
                )
            
            # Create event record
            event_data = self._prepare_event_data(data, event_datetime)
            event = self.event_model.create(event_data)
            
            # Convert to DTO
            result = self._event_to_dto(event)
            result.response_code = "000"
            result.response_message = "Event created successfully"
            result.response_message_ar = "تم إنشاء الحدث بنجاح"
            
            return request.make_json_response(result.to_dict(), status=201)
            
        except ValueError as ve:
            return self.handle_api_error(str(ve), "101", 400, "ValidationError")
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/v1/systems/<string:system_code>/events', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_system_events(self, system_code, event_type=None, building_id=None, 
                         floor_id=None, limit=100, offset=0, **kwargs):
        """
        Get events for a specific system.
        
        Path Parameters:
        - system_code: System identifier (fire|access|cctv|gate|pa|presence)
        
        Query Parameters:
        - event_type: Filter by event type
        - building_id: Filter by building ID
        - floor_id: Filter by floor ID
        - limit: Maximum number of results (default: 100)
        - offset: Pagination offset (default: 0)
        """
        try:
            if not validate_system_code(system_code):
                return self.handle_api_error(
                    f"Invalid system_code: {system_code}", "101", 400, "ValidationError"
                )
            
            # Forward to main get_events method with system_code filter
            return self.get_events(
                system_code=system_code,
                event_type=event_type,
                building_id=building_id,
                floor_id=floor_id,
                limit=limit,
                offset=offset,
                **kwargs
            )
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    @http.route('/api/v1/systems/<string:system_code>/status', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def get_system_status(self, system_code, **kwargs):
        """
        Get current status summary for a specific system.
        
        Path Parameters:
        - system_code: System identifier (fire|access|cctv|gate|pa|presence)
        """
        try:
            if not validate_system_code(system_code):
                return self.handle_api_error(
                    f"Invalid system_code: {system_code}", "101", 400, "ValidationError"
                )
            
            self.log_api_access(f'/api/v1/systems/{system_code}/status', 'GET')
            
            # Get latest events for the system
            domain = [('system_code', '=', system_code)]
            latest_events = self.event_model.search(
                domain, limit=10, order='event_time desc'
            )
            
            # Build status summary
            status_data = {
                "ResponseCode": "000",
                "ResponseMessage": "Success",
                "ResponseMessageAR": "نجح",
                # "SystemCode": system_code,
                # "SystemName": self._get_system_name(system_code),
                "LastUpdate": format_iso_datetime(latest_events[0].datetime) if latest_events else "",
                "EventCount": self.event_model.search_count(domain),
                "RecentEvents": [self._event_to_dto(event).to_dict() for event in latest_events[:5]]
            }
            
            return request.make_json_response(status_data, status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex)
    
    def _event_to_dto(self, event):
        """Convert event record to DTO"""
        dto = EventDTO(
            # Common envelope fields
            name=event.name or '',
            building_id=event.building_id.id,
            # building_code=event.building_code or '',
            floor_id=event.floor_id.id,
            # floor_code=event.floor_code or '',
            system_code=event.system_id.code,
            # system_name=event.system_name or '',
            event_type=event.event_type,
            datetime=format_iso_datetime(event.event_time),
            message=event.message or '',
            description=event.description or '',
            zone=event.zone_id.name or '',
            source_event_code=event.source_event_code or '',
            source_state=event.source_state or '',
            state=event.state or '',
            severity=event.severity or '',
            # command=event.command or ''
        )
        
        # Add system-specific fields based on system_code
        # self._populate_system_specific_fields(dto, event)
        
        return dto
    
    def _populate_system_specific_fields(self, dto, event):
        """Populate system-specific fields in DTO based on system_code"""
        if event.system_code == "fire":
            dto.panel_id = event.panel_id or 0
            dto.panel_code = event.panel_code or ''
            dto.panel_name = event.panel_name or ''
            dto.zone = event.zone or ''
            dto.loop = event.loop or ''
            dto.node_id = event.node_id or 0
            dto.node_code = event.node_code or ''
            dto.address = event.address or ''
            
        elif event.system_code == "access":
            dto.controller_id = event.controller_id or 0
            dto.controller_code = event.controller_code or ''
            dto.controller_name = event.controller_name or ''
            dto.reader_id = event.reader_id or 0
            dto.reader_code = event.reader_code or ''
            dto.card_id = event.card_id or 0
            dto.card_code = event.card_code or ''
            dto.user_id = event.user_id or 0
            dto.user_code = event.user_code or ''
            dto.door_id = event.door_id or ''
            dto.door_name = event.door_name or ''
            dto.vendor = event.vendor or ''
            dto.model = event.model or ''
            dto.holder = event.holder or ''
            dto.result = event.result or ''
            dto.reason = event.reason or ''
            dto.held_open_seconds = event.held_open_seconds or 0
            
        elif event.system_code == "cctv":
            dto.camera_id = event.camera_id or 0
            dto.camera_code = event.camera_code or ''
            dto.camera_name = event.camera_name or ''
            dto.location = event.location or ''
            dto.vendor = event.vendor or ''
            dto.model = event.model or ''
            dto.ip = event.ip or ''
            dto.channel = event.channel or 0
            dto.analytic_type = event.analytic_type or ''
            dto.count = event.count or 0
            dto.face_id = event.face_id or ''
            dto.uri = event.uri or ''
            dto.snapshot_url = event.snapshot_url or ''
            dto.recording = event.recording or False
            dto.last_heartbeat = event.last_heartbeat or ''
            
        elif event.system_code == "gate":
            dto.gate_id = event.gate_id or 0
            dto.gate_code = event.gate_code or ''
            dto.gate_name = event.gate_name or ''
            dto.location = event.location or ''
            dto.vendor = event.vendor or ''
            dto.model = event.model or ''
            dto.ip = event.ip or ''
            dto.status = event.status or ''
            dto.vehicle_plate = event.vehicle_plate or ''
            dto.trigger = event.trigger or ''
            dto.anpr_confidence = event.anpr_confidence or 0.0
            
        elif event.system_code == "pa":
            dto.zone_id = event.zone_id or 0
            dto.zone_code = event.zone_code or ''
            dto.zone_name = event.zone_name or ''
            dto.location = event.location or ''
            dto.vendor = event.vendor or ''
            dto.model = event.model or ''
            dto.volume = event.volume or 0
            dto.announcement_id = event.announcement_id or ''
            dto.script = event.script or ''
            dto.duration_sec = event.duration_sec or 0
            
        elif event.system_code == "presence":
            dto.sensor_id = event.sensor_id or 0
            dto.sensor_code = event.sensor_code or ''
            dto.sensor_name = event.sensor_name or ''
            dto.location = event.location or ''
            dto.vendor = event.vendor or ''
            dto.model = event.model or ''
            dto.sensor_type = event.sensor_type or ''
            dto.count = event.count or 0
            dto.occupancy = event.occupancy or False
    
    def _prepare_event_data(self, data, event_datetime):
        """Prepare event data for database insertion"""
        event_data = {
            # Common envelope fields
            'building_id': safe_int_conversion(data.get('buildingId')),
            # 'building_code': data.get('buildingCode', ''),
            'floor_id': safe_int_conversion(data.get('floorId')),
            # 'floor_code': data.get('floorCode', ''),
            'system_code': data.get('systemCode'),
            'system_name': data.get('systemName', self._get_system_name(data.get('systemCode'))),
            'event_type': data.get('eventType'),
            'datetime': event_datetime,
            'message': data.get('message'),
            'source_event_code': data.get('sourceEventCode', ''),
            'source_state': data.get('sourceState', ''),
            'state': data.get('state', ''),
            'command': data.get('command', '')
        }
        
        # Add system-specific fields from data object
        data_obj = data.get('data', {})
        if data_obj:
            event_data.update(self._extract_system_specific_data(data.get('systemCode'), data_obj))
        
        return event_data
    
    def _extract_system_specific_data(self, system_code, data_obj):
        """Extract system-specific data fields for database storage"""
        extracted = {}
        
        if system_code == "fire":
            extracted.update({
                'panel_id': safe_int_conversion(data_obj.get('id')),
                'panel_code': data_obj.get('panelCode', ''),
                'panel_name': data_obj.get('name', ''),
                'zone': data_obj.get('zone', ''),
                'loop': data_obj.get('loop', ''),
                'node_id': safe_int_conversion(data_obj.get('nodeId')),
                'node_code': data_obj.get('nodeCode', ''),
                'address': data_obj.get('address', '')
            })
            
        elif system_code == "access":
            extracted.update({
                'controller_id': safe_int_conversion(data_obj.get('id')),
                'controller_code': data_obj.get('controllerCode', ''),
                'controller_name': data_obj.get('name', ''),
                'reader_id': safe_int_conversion(data_obj.get('readerId')),
                'reader_code': data_obj.get('readerCode', ''),
                'card_id': safe_int_conversion(data_obj.get('cardId')),
                'card_code': data_obj.get('cardCode', ''),
                'user_id': safe_int_conversion(data_obj.get('userId')),
                'user_code': data_obj.get('userCode', ''),
                'door_id': data_obj.get('doorId', ''),
                'door_name': data_obj.get('doorName', ''),
                'vendor': data_obj.get('vendor', ''),
                'model': data_obj.get('model', ''),
                'holder': data_obj.get('holder', ''),
                'result': data_obj.get('result', ''),
                'reason': data_obj.get('reason', ''),
                'held_open_seconds': safe_int_conversion(data_obj.get('heldOpenSeconds'))
            })
            
        # Add similar blocks for other systems...
        # (cctv, gate, pa, presence)
        
        return extracted
    
    def _get_system_name(self, system_code):
        """Get human-readable system name"""
        system_names = {
            'fire': 'Fire Alarm',
            'access': 'Access Control',
            'cctv': 'CCTV',
            'gate': 'Gate Barrier',
            'pa': 'Public Address',
            'presence': 'Presence Sensors'
        }
        return system_names.get(system_code, system_code)
    
    @http.route('/api/v1/fast_test', type='http', auth='public',
                methods=['GET'], csrf=False, cors="*")
    def fast_test_event(self, system_code, **kwargs):
        """
        Fast test endpoint to generate events based on system_code parameter only.
        Generates all required data intelligently for testing purposes.
        
        Usage: GET /api/v1/fast_test?system_code=fire
        """
        try:
            # Validate system_code
            if not system_code:
                return self.handle_api_error(
                    ValueError("system_code parameter is required"),
                    response_code="101",
                    status=400,
                    message="Missing required parameter: system_code"
                )
            
            if not validate_system_code(system_code):
                return self.handle_api_error(
                    ValueError(f"Invalid system_code: {system_code}"),
                    response_code="101", 
                    status=400,
                    message=f"Invalid system_code. Must be one of: fire, access, cctv, gate, pa, presence"
                )
            
            # Generate smart test data based on system_code
            test_data = self._generate_smart_test_data(system_code)
            
            # Create the event
            event = self.event_model.create(test_data)
            
            # Convert to DTO for response
            dto = self._event_to_dto(event)
            dto.response_code = '000'
            dto.response_message = 'Test event created successfully'
            dto.response_message_ar = 'تم إنشاء حدث الاختبار بنجاح'
            
            return request.make_json_response(dto.to_dict(), status=200)
            
        except Exception as ex:
            return self.handle_api_error(ex, response_code="100", status=500)
    
    def _generate_smart_test_data(self, system_code):
        """Generate intelligent test data based on system_code"""
        from datetime import datetime
        import random
        
        # Base event data
        base_data = {
            'building_id': 1,
            # 'building_code': 'HQ-001',
            'floor_id': random.randint(1, 5),
            # 'floor_code': f'F{random.randint(1, 5)}' or '',
            'system_code': system_code,
            'system_name': self._get_system_name(system_code),
            'datetime': datetime.now(),
            'message': f'Test {system_code} event generated automatically',
            'source_state': random.choice(['closed', 'open', 'active', 'inactive']),
            'state': random.choice(['normal', 'alarm', 'fault', 'test']),
        }
        
        # System-specific data generation
        if system_code == 'fire':
            base_data.update({
                'event_type': random.choice(['alarm', 'fault', 'test', 'restore']),
                'source_event_code': f'FP-{random.randint(100, 999)}',
                'panel_id': random.randint(1, 10),
                'panel_code': f'FP-{random.randint(1, 10):02d}',
                'panel_name': f'Fire Panel {random.randint(1, 10)}',
                'zone': f'Zone-{random.randint(1, 16)}',
                'loop': f'Loop-{random.randint(1, 4)}',
                'node_id': random.randint(1, 100),
                'node_code': f'FD-{random.randint(100, 999)}',
                'address': f'{random.randint(1, 255)}'
            })
            
        elif system_code == 'access':
            base_data.update({
                'event_type': random.choice(['entry', 'exit', 'denied', 'forced']),
                'source_event_code': f'AC-{random.randint(100, 999)}',
                'controller_id': random.randint(1, 20),
                'controller_code': f'AC-{random.randint(1, 20):02d}',
                'controller_name': f'Access Controller {random.randint(1, 20)}',
                'reader_id': random.randint(1, 50),
                'reader_code': f'RD-{random.randint(100, 999)}',
                'card_id': random.randint(1000, 9999),
                'card_code': f'CD-{random.randint(10000, 99999)}',
                'user_id': random.randint(1, 500),
                'user_code': f'USR-{random.randint(1000, 9999)}',
                'door_id': f'DR-{random.randint(100, 999)}',
                'door_name': f'Door {random.randint(1, 100)}',
                'result': random.choice(['granted', 'denied', 'timeout']),
                'reason': random.choice(['schedule', 'invalidCard', 'expired', 'antipassback', 'pinRequired']),
                'held_open_seconds': random.randint(5, 30)
            })
            
        elif system_code == 'cctv':
            base_data.update({
                'event_type': random.choice(['motion', 'recording', 'offline', 'online']),
                'source_event_code': f'CAM-{random.randint(100, 999)}',
                'camera_id': random.randint(1, 100),
                'camera_code': f'CAM-{random.randint(1, 100):03d}',
                'camera_name': f'Camera {random.randint(1, 100)}',
                'location': f'Location-{random.randint(1, 50)}',
                'ip': f'192.168.1.{random.randint(100, 200)}',
                'channel': random.randint(1, 16),
                'analytic_type': random.choice(['motion', 'lineCrossing', 'loitering', 'peopleCount']),
                'count': random.randint(1, 10),
                'recording': random.choice([True, False])
            })
            
        elif system_code == 'gate':
            base_data.update({
                'event_type': random.choice(['open', 'close', 'vehicle_detected', 'fault']),
                'source_event_code': f'GT-{random.randint(100, 999)}',
                'gate_id': random.randint(1, 20),
                'gate_code': f'GT-{random.randint(1, 20):02d}',
                'gate_name': f'Gate {random.randint(1, 20)}',
                'status': random.choice(['open', 'closed', 'opening', 'closing']),
                'vehicle_plate': f'ABC-{random.randint(1000, 9999)}',
                'trigger': random.choice(['manual', 'card', 'remote', 'anpr']),
                'anpr_confidence': round(random.uniform(0.7, 1.0), 2)
            })
            
        elif system_code == 'pa':
            base_data.update({
                'event_type': random.choice(['announcement', 'emergency', 'test', 'fault']),
                'source_event_code': f'PA-{random.randint(100, 999)}',
                'zone_id': random.randint(1, 50),
                'zone_code': f'PA-Z{random.randint(1, 50):02d}',
                'zone_name': f'PA Zone {random.randint(1, 50)}',
                'volume': random.randint(1, 10),
                'announcement_id': f'ANN-{random.randint(1000, 9999)}',
                'script': f'Test announcement script {random.randint(1, 100)}',
                'duration_sec': random.randint(10, 300)
            })
            
        elif system_code == 'presence':
            base_data.update({
                'event_type': random.choice(['occupied', 'vacant', 'motion', 'fault']),
                'source_event_code': f'PR-{random.randint(100, 999)}',
                'sensor_id': random.randint(1, 200),
                'sensor_code': f'PR-{random.randint(1, 200):03d}',
                'sensor_name': f'Presence Sensor {random.randint(1, 200)}',
                'sensor_type': random.choice(['pir', 'microwave', 'ultrasonic', 'camera']),
                'occupancy': random.choice([True, False])
            })
        
        return base_data