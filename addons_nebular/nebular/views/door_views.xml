<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Door List View -->
    <record id="nebular_door_view_list" model="ir.ui.view">
        <field name="name">nebular.door.list</field>
        <field name="model">nebular.door</field>
        <field name="arch" type="xml">
            <list string="Doors">
                <field name="name"/>
                <field name="door_number"/>
                <field name="door_type"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <field name="is_active"/>
                <field name="is_locked"/>
                <field name="device_count"/>
            </list>
        </field>
    </record>

    <!-- Door Form View -->
    <record id="nebular_door_view_form" model="ir.ui.view">
        <field name="name">nebular.door.form</field>
        <field name="model">nebular.door</field>
        <field name="arch" type="xml">
            <form string="Door">
                <header>
                    <button name="action_lock" type="object" string="Lock" 
                            invisible="is_locked" class="btn-primary"/>
                    <button name="action_unlock" type="object" string="Unlock" 
                            invisible="not is_locked" class="btn-secondary"/>
                    <button name="action_lock" type="object" string="Lock"
                            invisible="is_locked" class="btn-warning"/>
                    <button name="action_unlock" type="object" string="Unlock"
                            invisible="not is_locked" class="btn-success"/>
                    <button name="action_view_devices" type="object" string="View Devices" 
                            invisible="device_count == 0" class="btn-secondary"/>
                    <button name="action_view_events" type="object" string="View Events" 
                            invisible="event_count == 0" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_devices" type="object" class="oe_stat_button" icon="fa-cog">
                            <field name="device_count" widget="statinfo" string="Devices"/>
                        </button>
                        <button name="action_view_events" type="object" class="oe_stat_button" icon="fa-calendar">
                            <field name="event_count" widget="statinfo" string="Events"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="door_number"/>
                            <field name="door_type"/>
                            <field name="is_active"/>
                            <field name="is_locked"/>
                        </group>
                        <group>
                            <field name="building_id"/>
                            <field name="zone_id"/>
                            <field name="floor_id"/>
                            <field name="room_id"/>
                            <field name="system_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Detailed description of the door..."/>
                        </page>
                        <page string="Access Control" name="access">
                            <group>
                                <group>
                                    <field name="access_level"/>
                                    <field name="is_open"/>
                                    <field name="is_alert"/>
                                </group>
                                <group>
                                    <field name="last_access_time"/>
                                    <field name="access_event_count"/>
                                    <field name="security_status"/>
                                </group>
                            </group>
                        </page>
                        <page string="Devices" name="devices" invisible="device_count == 0">
                            <field name="device_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="device_type_id"/>
                                    <field name="status_display"/>
                                    <field name="is_active"/>
                                </list>
                            </field>
                        </page>
                        <page string="Events" name="events" invisible="event_count == 0">
                            <field name="event_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="severity"/>
                                    <field name="state"/>
                                    <field name="event_time"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Door Search View -->
    <record id="nebular_door_view_search" model="ir.ui.view">
        <field name="name">nebular.door.search</field>
        <field name="model">nebular.door</field>
        <field name="arch" type="xml">
            <search string="Doors">
                <field name="name"/>
                <field name="door_number"/>
                <field name="door_type"/>
                <field name="building_id"/>
                <field name="floor_id"/>
                <field name="room_id"/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <filter string="Locked" name="locked" domain="[('is_locked', '=', True)]"/>
                <filter string="Unlocked" name="unlocked" domain="[('is_locked', '=', False)]"/>
                <filter string="Alert Status" name="alert" domain="[('is_alert', '=', True)]"/>
                <filter string="Open Doors" name="open" domain="[('is_open', '=', True)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Door Type" name="group_door_type" context="{'group_by': 'door_type'}"/>
                    <filter string="Building" name="group_building" context="{'group_by': 'building_id'}"/>
                    <filter string="Floor" name="group_floor" context="{'group_by': 'floor_id'}"/>
                    <filter string="Room" name="group_room" context="{'group_by': 'room_id'}"/>
                    <filter string="Access Level" name="group_access_level" context="{'group_by': 'access_level'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Door Action -->
    <record id="nebular_door_action" model="ir.actions.act_window">
        <field name="name">Doors</field>
        <field name="res_model">nebular.door</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first door!
            </p>
            <p>
                Doors are access points in your building that can be monitored and controlled.
            </p>
        </field>
    </record>

</odoo>